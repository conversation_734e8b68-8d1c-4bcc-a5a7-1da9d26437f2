FROM php:8.2-fpm

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip

# 清理缓存
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# 安装PHP扩展
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd intl zip \
    && pecl install redis \
    && docker-php-ext-enable redis

# 安装 Node.js 22 和 npm
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs

# 安装Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 复制PHP配置
COPY ./docker-compose/php/php.ini /usr/local/etc/php/conf.d/uploads.ini

# 设置工作目录
WORKDIR /var/www

# 复制应用代码
COPY . /var/www

# 设置权限
#RUN chown -R www-data:www-data /var/www

# 使用www-data用户
USER root