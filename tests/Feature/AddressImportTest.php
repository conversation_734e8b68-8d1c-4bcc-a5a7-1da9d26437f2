<?php

namespace Tests\Feature;

use App\Models\Address;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class AddressImportTest extends TestCase
{
    use RefreshDatabase;

    /**
     * 测试从 pca-code.json 文件批量导入地址数据到 addresses 表
     */
    public function test_import_addresses_from_pca_code_json(): void
    {
        // 确保表是空的
        $this->assertEquals(0, Address::count());

        // 读取 JSON 文件
        $jsonPath = resource_path('pca-code.json');
        $this->assertFileExists($jsonPath, 'pca-code.json 文件不存在');

        $jsonContent = file_get_contents($jsonPath);
        $this->assertNotFalse($jsonContent, '无法读取 pca-code.json 文件');

        $data = json_decode($jsonContent, true);
        $this->assertIsArray($data, 'JSON 数据格式不正确');
        $this->assertNotEmpty($data, 'JSON 数据为空');

        // 准备批量插入的数据
        $addressesToInsert = [];

        // 递归处理数据结构
        $this->processAddressData($data, '', $addressesToInsert);

        // 批量插入数据
        $this->assertNotEmpty($addressesToInsert, '没有找到可插入的地址数据');

        // 使用事务批量插入
        DB::transaction(function () use ($addressesToInsert) {
            // 分批插入，避免单次插入数据过多
            $chunks = array_chunk($addressesToInsert, 1000);
            foreach ($chunks as $chunk) {
                Address::insert($chunk);
            }
        });

        // 验证插入结果
        $totalCount = Address::count();
        $this->assertGreaterThan(0, $totalCount, '没有成功插入任何地址数据');

        // 验证数据结构
        $provinces = Address::where('parent_code', '')->get();
        $this->assertGreaterThan(0, $provinces->count(), '没有找到省级数据');

        // 验证具体数据 - 检查北京市是否存在
        $beijing = Address::where('code', '11')->where('name', '北京市')->first();
        $this->assertNotNull($beijing, '北京市数据未正确插入');
        $this->assertEquals('', $beijing->parent_code, '北京市的父级代码应为空');

        // 验证市级数据 - 检查北京市辖区
        $beijingDistricts = Address::where('code', '1101')->where('name', '市辖区')->first();
        $this->assertNotNull($beijingDistricts, '北京市辖区数据未正确插入');
        $this->assertEquals('11', $beijingDistricts->parent_code, '北京市辖区的父级代码应为11');

        // 验证区级数据 - 检查东城区
        $dongcheng = Address::where('code', '110101')->where('name', '东城区')->first();
        $this->assertNotNull($dongcheng, '东城区数据未正确插入');
        $this->assertEquals('1101', $dongcheng->parent_code, '东城区的父级代码应为1101');

        echo "\n成功导入 {$totalCount} 条地址数据\n";
        echo "省级数据: " . $provinces->count() . " 条\n";
        echo "市级数据: " . Address::whereRaw('LENGTH(code) = 4')->count() . " 条\n";
        echo "区县级数据: " . Address::whereRaw('LENGTH(code) = 6')->count() . " 条\n";
    }

    /**
     * 递归处理地址数据
     *
     * @param array $data 地址数据数组
     * @param string $parentCode 父级代码
     * @param array &$addressesToInsert 要插入的地址数据引用
     */
    private function processAddressData(array $data, string $parentCode, array &$addressesToInsert): void
    {
        foreach ($data as $item) {
            // 添加当前级别的地址
            $addressesToInsert[] = [
                'code' => $item['code'],
                'parent_code' => $parentCode,
                'name' => $item['name'],
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // 如果有子级数据，递归处理
            if (isset($item['children']) && is_array($item['children']) && !empty($item['children'])) {
                $this->processAddressData($item['children'], $item['code'], $addressesToInsert);
            }
        }
    }

    /**
     * 测试地址数据的层级关系
     */
    public function test_address_hierarchy_relationships(): void
    {
        // 先导入数据
        $this->test_import_addresses_from_pca_code_json();

        // 测试层级关系
        $beijing = Address::where('code', '11')->first();
        $this->assertNotNull($beijing);

        // 查找北京的子级
        $beijingChildren = Address::where('parent_code', '11')->get();
        $this->assertGreaterThan(0, $beijingChildren->count());

        // 查找市辖区的子级
        $districts = Address::where('parent_code', '1101')->get();
        $this->assertGreaterThan(0, $districts->count());

        // 验证东城区的层级路径
        $dongcheng = Address::where('code', '110101')->first();
        $this->assertNotNull($dongcheng);
        
        $shiXiaQu = Address::where('code', $dongcheng->parent_code)->first();
        $this->assertNotNull($shiXiaQu);
        $this->assertEquals('市辖区', $shiXiaQu->name);
        
        $province = Address::where('code', $shiXiaQu->parent_code)->first();
        $this->assertNotNull($province);
        $this->assertEquals('北京市', $province->name);
    }
}
