<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum UnitEnum: string implements HasLabel
{
    case Piece = '个';
    case Branch = '支';
    // 盒
    case Box = '盒';
    case Item = '件';
    case Pair = '双';
    case Set = '套';
    case Case = '箱';
    case Kg = 'kg';
    case G = 'g';
    case M = 'm';
    case Cm = 'cm';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Piece => '个',
            self::Branch => '支',
            self::Item => '件',
            self::Pair => '双',
            self::Set => '套',
            self::Box => '盒',
            self::Case => '箱',
            self::Kg => 'kg',
            self::G => 'g',
            self::M => 'm',
            self::Cm => 'cm',
        };
    }
}
