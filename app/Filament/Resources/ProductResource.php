<?php

namespace App\Filament\Resources;

use App\Enums\UnitEnum;
use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Models\Product;
use App\Utils\CommonUtil;
use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductResource extends BaseResource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('product_category_id')
                    ->label('产品分类')
                    ->required()
                    ->relationship('category', 'name')
                    ->preload()
                    ->searchable(),
                Forms\Components\SpatieMediaLibraryFileUpload::make('image_url')
                    ->label('产品图片')
                    ->image()
                    ->imageResizeMode('cover')
                    ->imageCropAspectRatio('1:1')
                    ->panelAspectRatio('1:1')
                    ->extraAttributes(['style' => 'max-width: 200px;']),
                Forms\Components\TextInput::make('name')
                    ->label('产品名称')
                    ->required()
                    ->maxLength(100),
                Forms\Components\TextInput::make('spec')
                    ->label('规格')
                    ->required()
                    ->maxLength(100),
                Forms\Components\TextInput::make('desc')
                    ->label('描述')
                    ->maxLength(255)->columnSpanFull(),
                Forms\Components\Select::make('unit')
                    ->label('计量单位')
                    ->required()
                    ->options(UnitEnum::class)
                    ->required(),
                Fieldset::make('价格')
                    ->columns(3)
                    ->schema([
                        Forms\Components\TextInput::make('purchase_price')
                            ->label('建议采购单价')
                            ->required()
                            ->numeric()
                            ->default(0.00),
                        Forms\Components\TextInput::make('selling_price')
                            ->label('建议销售单价')
                            ->required()
                            ->numeric()
                            ->default(0.00),
                        Forms\Components\TextInput::make('overseas_selling_price')
                            ->label('海外销售单价')
                            ->required()
                            ->numeric()
                            ->default(0.00),
                    ]),
                Fieldset::make('单品体积重量')
                    ->columns(3)
                    ->schema([
                        Forms\Components\TextInput::make('size')
                            ->label('规格尺寸(cm) 长*宽*高')
                            ->suffix('cm')
                            ->columnSpan(1)
                            ->maxLength(30)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Forms\Set $set, ?string $state) {
                                self::calcVolume($state, $set, 'volume');
                            }),
                        Forms\Components\TextInput::make('volume')
                            ->label('规格体积(cm³)')
                            ->suffix('cm³')
//                            ->required()
                            ->columnSpan(1)
                            ->numeric()
                            ->default(0.00)
                            ->readOnly(),
                        Forms\Components\TextInput::make('weight')
                            ->label('规格重量(g) 如:12g')
                            ->suffix('g')
                            ->columnSpan(1)
                            ->numeric()
                            ->default(0.00)
                            ->live(true)
                            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, ?string $state) {
                                // 当 weight 修改时，更新 package_info 和 overseas_package_info 相关的重量
                                if (is_numeric($state) && $state > 0) {
                                    // 更新 package_weight
                                    $packageInfo = $get('package_info');
                                    if (is_numeric($packageInfo)) {
                                        $packageWeight = $state * $packageInfo / 1000;
                                        $set('package_weight', number_format($packageWeight, 2, '.', ''));
                                    }

                                    // 更新 overseas_package_weight
                                    $overseasPackageInfo = $get('overseas_package_info');
                                    if (is_numeric($overseasPackageInfo)) {
                                        $overseasPackageWeight = $state * $overseasPackageInfo / 1000;
                                        $set('overseas_package_weight', number_format($overseasPackageWeight, 2, '.', ''));
                                    }
                                }
                            }),
                    ]),
                Fieldset::make('国内包装体积重量')
                    ->columns(4)
                    ->schema([
                        Forms\Components\TextInput::make('package_info')
                            ->label('装箱(袋)数 如:100')
                            ->numeric()
                            ->default(0)
                            ->maxLength(20)
                            ->live(true)
                            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, ?string $state) {
                                // 当 package_info 修改时，更新 package_weight
                                $weight = $get('weight');
                                if (is_numeric($state) && is_numeric($weight) && $weight > 0) {
                                    $packageWeight = $weight * $state / 1000;
                                    $set('package_weight', number_format($packageWeight, 2, '.', ''));
                                }
                            }),
                        Forms\Components\TextInput::make('package_size')
                            ->label('包装尺寸(cm) 长*宽*高')
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Forms\Set $set, ?string $state) {
                                self::calcVolume($state, $set, 'package_volume');
                            })
                            ->suffix('cm'),
                        Forms\Components\TextInput::make('package_volume')
                            ->label('包装体积(cm³)')
                            ->readOnly()
                            ->suffix('cm³')
                            ->numeric()
                            ->default(0.00),
                        Forms\Components\TextInput::make('package_weight')
                            ->label('包装重量(kg) 如:1kg')
                            ->numeric()
                            ->suffix('kg')
                            ->default(0.00)
                            ->live(true),
                    ]),
                Fieldset::make('海外包装体积重量')
                    ->columns(4)
                    ->schema([
                        Forms\Components\TextInput::make('overseas_package_info')
                            ->label('装箱数 如:100')
                            ->numeric()
                            ->default(0)
                            ->maxLength(20)
                            ->live(true)
                            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, ?string $state) {
                                // 当 package_info 修改时，更新 package_weight
                                $weight = $get('weight');
                                if (is_numeric($state) && is_numeric($weight) && $weight > 0) {
                                    $packageWeight = $weight * $state / 1000;
                                    $set('overseas_package_weight', number_format($packageWeight, 2, '.', ''));
                                }
                            }),
                        Forms\Components\TextInput::make('overseas_package_size')
                            ->label('海外包装尺寸(cm) 长*宽*高')
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Forms\Set $set, ?string $state) {
                                self::calcVolume($state, $set, 'overseas_package_volume');
                            })
                            ->suffix('cm')
                            ->maxLength(30),
                        Forms\Components\TextInput::make('overseas_package_volume')
                            ->label('海外包装体积(cm³)')
                            ->suffix('cm³')
                            ->readOnly()
                            ->numeric()
                            ->default(0.00),
                        Forms\Components\TextInput::make('overseas_package_weight')
                            ->label('海外包装重量(kg) 如:1kg')
                            ->suffix('kg')
                            ->numeric()
                            ->default(0.00),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('category.name')
                    ->label('产品分类')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\SpatieMediaLibraryImageColumn::make('image_url')
                    ->label('产品图片'),
                Tables\Columns\TextColumn::make('name')
                    ->label('产品名称')
                    ->searchable(),
                Tables\Columns\TextColumn::make('spec')
                    ->label('规格')
                    ->searchable(),
                Tables\Columns\TextColumn::make('desc')
                    ->label('描述')
                    ->searchable(),
                Tables\Columns\TextColumn::make('unit')
                    ->label('计量单位')
                    ->searchable(),
                Tables\Columns\TextColumn::make('purchase_price')
                    ->label('建议采购单价')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('selling_price')
                    ->label('建议销售单价')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('overseas_selling_price')
                    ->label('海外销售单价')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('size')
                    ->label('规格尺寸(cm)')
                    ->searchable(),
                Tables\Columns\TextColumn::make('weight')
                    ->label('规格重量(g)')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('volume')
                    ->label('规格体积(cm³)')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('package_info')
                    ->label('装箱(袋)数')
                    ->searchable(),
                Tables\Columns\TextColumn::make('package_size')
                    ->label('包装尺寸(cm)')
                    ->searchable(),
                Tables\Columns\TextColumn::make('package_volume')
                    ->label('包装体积(cm³)')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('package_weight')
                    ->label('包装重量(kg)')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('overseas_package_info')
                    ->label('海外装箱数')
                    ->searchable(),
                Tables\Columns\TextColumn::make('overseas_package_size')
                    ->label('海外包装尺寸(cm)')
                    ->searchable(),
                Tables\Columns\TextColumn::make('overseas_package_volume')
                    ->label('海外包装体积(cm³)')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('overseas_package_weight')
                    ->label('海外包装重量(kg)')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label('删除时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }

    /**
     * @param string|null $state
     * @param Forms\Set $set
     * @return void
     */
    public static function calcVolume(?string $state, Forms\Set $set, string $field): void
    {
        if (!$state) {
            return;
        }
        // 解析尺寸字符串 (例如: 10*20*30)
        $dimensions = array_map('trim', explode('*', $state));
        if (count($dimensions) === 3) {
            $length = floatval($dimensions[0]);
            $width = floatval($dimensions[1]);
            $height = floatval($dimensions[2]);

            if ($length > 0 && $width > 0 && $height > 0) {
                // 计算体积 = 长 * 宽 * 高
                $volume = $length * $width * $height;
                $set($field, round($volume, 2));
            }
        }
    }
}
