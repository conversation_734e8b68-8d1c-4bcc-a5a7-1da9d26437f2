<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SalesOrderResource\Pages;
use App\Filament\Resources\SalesOrderResource\RelationManagers;
use App\Models\SalesOrder;
use App\Models\Product;
use App\Utils\CommonUtil;
use App\Enums\UnitEnum;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Icetalker\FilamentTableRepeater\Forms\Components\TableRepeater;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Repeater;
use Log;

class SalesOrderResource extends BaseResource
{
    protected static ?string $model = SalesOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = '销售管理';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('order_number')
                    ->required()
                    ->default(CommonUtil::generateOrderNumber('SO'))
                    ->maxLength(50),
                Select::make('customer_id')
                    ->relationship('customer', 'name')
                    ->required(),
                Select::make('contact_id')
                    ->relationship('contact', 'name'),
                TextInput::make('opportunity_id')
                    ->hidden()
                    ->numeric(),
                DatePicker::make('order_date')
                    ->default(date('Y-m-d'))
                    ->maxDate(now())
                    ->required(),
                DatePicker::make('requested_delivery_date'),
                TextInput::make('shipping_address')
                    ->maxLength(255),
                TableRepeater::make('items')
                    ->label('订单详情')
                    ->relationship()
                    ->reorderable()
                    ->colStyles([
                        'unit' => 'width: 50px;',
                        'quantity' => 'width: 100px;',
                        'unit_price' => 'width: 100px;',
                        'total_line_amount' => 'width: 100px;',
                    ])
                    ->schema([
                        Select::make('product_id')
                            ->relationship('product', 'name', ignoreRecord: true)
                            ->required()
                            ->searchable()
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                if ($state) {
                                    $product = Product::find($state);
                                    if ($product) {
                                        $set('unit_price', $product->selling_price ?? 0);
                                        $set('unit', $product->unit ?? '');
                                        $set('name', $product->name ?? '');
                                        $set('spec', $product->spec ?? '');

                                        // 设置默认数量并触发计算
                                        $set('quantity', 1);

                                        // 手动触发计算逻辑，因为$set()不会自动触发afterStateUpdated
                                        $quantity = 1;
                                        $unitPrice = $product->selling_price ?? 0;
                                        $lineAmount = bcmul($quantity, $unitPrice);
                                        $set('total_line_amount', CommonUtil::formatToAmount($lineAmount));

                                        // 更新总金额
                                        self::updateTotalAmount($get, $set, true);
                                    }
                                }
                            })
                            ->columnSpan(1),
                        TextInput::make('name')
                            ->maxLength(100)
                            ->columnSpan(1),
                        TextInput::make('spec')
                            ->maxLength(100)
                            ->columnSpan(2),
                        TextInput::make('unit')
                            ->maxLength(20)
                            ->readOnly()
                            ->columnSpan(1),
                        TextInput::make('quantity')
                            ->required()
                            ->numeric()
                            ->default(1)
                            ->live()
                            ->columnSpan(1)
                            ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                // 当数量更新时，重新计算行总额
                                $quantity = $get('quantity') ?? 0;
                                $unitPrice = $get('unit_price') ?? 0;
                                $lineAmount = bcmul($quantity, $unitPrice);
                                $set('total_line_amount', CommonUtil::formatToAmount($lineAmount));

                                // 更新总金额
                                self::updateTotalAmount($get, $set, true);
                            }),
                        TextInput::make('unit_price')
                            ->required()
                            ->numeric()
                            ->default(0.00)
                            ->live()
                            ->columnSpan(1)
//                            ->prefix('￥')
                            ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                // 当单价更新时，重新计算行总额
                                $quantity = $get('quantity') ?? 0;
                                $unitPrice = $get('unit_price') ?? 0;
                                $lineAmount = bcmul($quantity, $unitPrice);
                                $set('unit_price', $unitPrice);
                                $set('total_line_amount', CommonUtil::formatToAmount($lineAmount));

                                // 更新总金额
                                self::updateTotalAmount($get, $set, true);
                            }),
//                            TextInput::make('discount_rate')
//                                ->required()
//                                ->numeric()
//                                ->default(0.00)
//                                ->suffix('%')
//                                ->columnSpan(1),
//                            TextInput::make('tax_rate')
//                                ->required()
//                                ->numeric()
//                                ->default(0.0000)
//                                ->suffix('%')
//                                ->columnSpan(1),
                        TextInput::make('total_line_amount')
                            ->required()
                            ->numeric()
                            ->default(0.00)
                            ->readOnly()
//                            ->prefix('￥')
                            ->live()
                            ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                self::updateTotalAmount($get, $set);
                            })
                            ->columnSpan(1),
//                            TextInput::make('shipped_quantity')
//                                ->required()
//                                ->numeric()
//                                ->default(0.00)
//                                ->columnSpan(1),

                        TextInput::make('remark')
                            ->maxLength(255)
                            ->columnSpan(2),
                    ]),
                TextInput::make('original_amount')
                    ->required()
                    ->numeric()
                    ->default(0.00)
                    ->readOnly(),
                TextInput::make('discount_amount')
                    ->required()
                    ->numeric()
                    ->default(0.00)
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                        self::updateTotalAmount($get, $set);
                    }),
                TextInput::make('total_amount')
                    ->required()
                    ->numeric()
                    ->default(0.00)
                    ->live(onBlur: true)
                    ->readOnly()
                    ->afterStateUpdated(function ($state, Forms\Set $set) {
                        $str = CommonUtil::convertAmountToChinese($state);
                        $set('total_amount_in_words', $str);
                    }),
                TextInput::make('total_amount_in_words')
                    ->required()
                    ->maxLength(255)
                    ->readOnly(),
                TextInput::make('status')
                    ->required()
                    ->maxLength(20)
                    ->default('pending'),
                TextInput::make('payment_method')
                    ->maxLength(50),
                TextInput::make('salesperson_id')
                    ->numeric(),
                TextInput::make('attachments'),
                TextInput::make('images'),
                Forms\Components\Textarea::make('remark')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('customer.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('contact.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('opportunity_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('order_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('requested_delivery_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('shipping_address')
                    ->searchable(),
                Tables\Columns\TextColumn::make('original_amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('discount_amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_amount_in_words')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->searchable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->searchable(),
                Tables\Columns\TextColumn::make('salesperson_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSalesOrders::route('/'),
            'create' => Pages\CreateSalesOrder::route('/create'),
            'edit' => Pages\EditSalesOrder::route('/{record}/edit'),
        ];
    }

    /**
     * 更新订单总金额
     *
     * @param Forms\Get $get
     * @param Forms\Set $set
     * @return void
     */
    public static function updateTotalAmount(Forms\Get $get, Forms\Set $set, $isInSubtable = false): void
    {
        $prefix = $isInSubtable ? '../../' : '';
        $items = $get($prefix . 'items');
        if (empty($items)) {
            return;
        }

        $originalAmount = 0;
        $totalAmount = 0;

        foreach ($items as $item) {
            $quantity = $item['quantity'] ?? 0;
            $unitPrice = $item['unit_price'] ?? 0;
            $discountRate = $item['discount_rate'] ?? 0;
            $taxRate = $item['tax_rate'] ?? 0;

            // 计算行总额
            $lineTotal = $quantity * $unitPrice;

            // 应用折扣
//            $discountedAmount = $lineTotal * (1 - $discountRate / 100);

            // 应用税费
//            $finalLineAmount = $discountedAmount * (1 + $taxRate / 100);

            // 更新行总额字段
//            if (isset($item['total_line_amount']) && $item['total_line_amount'] != $finalLineAmount) {
//                // 注意：这里我们不直接设置，因为可能会导致无限循环
//            }


            $originalAmount += $lineTotal;
//            $totalAmount += $finalLineAmount;
            $totalAmount += $lineTotal;
        }
        $discountAmount = $get($prefix.'discount_amount') ?? 0;
        // 计算折扣金额
//        $discountAmount = $originalAmount - $totalAmount;
        $totalAmount = bcsub($originalAmount, $discountAmount);
        // 设置总金额字段，保留两位小数
        $set($prefix . 'original_amount', CommonUtil::formatToAmount($originalAmount));
        $set($prefix . 'discount_amount', $discountAmount);
        $set($prefix . 'total_amount', CommonUtil::formatToAmount($totalAmount));

        // 更新中文大写金额
        $str = CommonUtil::convertAmountToChinese($totalAmount);
        $set($prefix . 'total_amount_in_words', $str);
    }
}
