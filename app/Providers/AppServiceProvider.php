<?php

namespace App\Providers;

use Filament\Forms\Components\Field;
use Filament\Tables\Columns\Column;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // 解除所有模型的保护
        Model::unguard();
        Field::configureUsing(function (Field $component): void {
            // 默认开启翻译
            $component->translateLabel();
            $label = $component->getName();
            $component->label('attributes.'.$label);
        });
        Column::configureUsing(function (Column $component): void {
            // 默认开启翻译
            $component->translateLabel();
            $label = $component->getName();
            $component->label('attributes.'.$label);
        });
    }
}
